# ConditionalFileBatchDeleter 数据库模式使用指南

## 概述

`ConditionalFileBatchDeleter` 类现已支持基于PostgreSQL数据库查询的批量文件删除功能。这个新模式允许用户直接从数据库中查询标注数据，而不需要依赖JSON文件，提供了更高效和灵活的文件管理方案。

## 新增功能特性

### 1. 数据库查询模式
- 支持PostgreSQL数据库连接和查询
- 可查询`segmentation_data`或`obb_data`列
- 支持数据源筛选条件
- 自动标签提取和条件评估

### 2. UI控件配置
新增以下UI控件支持：
- `rotate_146`: segment模式选择checkbox
- `rotate_147`: obb模式选择checkbox  
- `lineEdit_174`: 数据库名称输入框
- `lineEdit_175`: 数据库表名输入框
- `lineEdit_176`: 数据源筛选条件输入框（可选）

### 3. 智能模式选择
- 根据checkbox状态自动确定查询列
- 支持segment和obb两种标注模式
- 优先级：segment > obb（当两个都选择时）

## 使用方法

### 1. 基本配置

```python
from anylabeling.customize_ui.src.ui_operate.save_data.helper1 import ConditionalFileBatchDeleter
from anylabeling.customize_ui.src.ui_utils import LineEditManager, TextEditManager, CheckBoxManager

# 创建管理器实例
line_edit_manager = LineEditManager(path_input_widget)
text_edit_manager = TextEditManager(result_display_widget)
checkbox_manager = CheckBoxManager(checkbox_container)  # 新增
logger = Logger()
log_output = LogOutput(log_display_widget)

# 创建批量删除器
deleter = ConditionalFileBatchDeleter(
    line_edit_manager=line_edit_manager,
    text_edit_manager=text_edit_manager,
    checkbox_manager=checkbox_manager,  # 新增参数
    logger=logger,
    log_output=log_output
)
```

### 2. 数据库模式使用

```python
# 方式1: 从UI控件获取所有参数
result = deleter.execute_database_batch_deletion()

# 方式2: 指定部分参数
result = deleter.execute_database_batch_deletion(
    folder_path="/path/to/image/files",
    condition_expression="主任务 >= 1"
)

# 方式3: 复杂条件表达式
result = deleter.execute_database_batch_deletion(
    folder_path="/path/to/image/files", 
    condition_expression="(主任务 >= 1 AND XXX >= 1) OR YYY >= 2"
)
```

### 3. 结果处理

```python
if result["success"]:
    print(f"操作成功完成!")
    print(f"扫描文件数: {result['scanned_files']}")
    print(f"匹配条件文件数: {result['matched_files']}")
    print(f"删除图像文件数: {result['deleted_image_files']}")
    print(f"删除JSON文件数: {result['deleted_json_files']}")
    print(f"失败删除数: {result['failed_deletions']}")
    
    if result.get('summary_report'):
        print(f"总结报告:\n{result['summary_report']}")
else:
    print(f"操作失败: {result['error_message']}")
    if result.get('errors'):
        print("详细错误:")
        for error in result['errors']:
            print(f"  - {error}")
```

## 数据库配置

### 1. 连接参数
```python
# 默认连接配置（硬编码）
host = "localhost"
port = 5432
database = "从UI控件获取"  # lineEdit_174
user = "postgres"
password = "123456"
```

### 2. 表结构要求
数据库表应包含以下列：
```sql
CREATE TABLE your_table_name (
    image_path TEXT,                    -- 图像文件路径
    segmentation_data JSONB,           -- 分割标注数据
    obb_data JSONB,                    -- OBB标注数据  
    data_source TEXT                   -- 数据源标识（可选）
);
```

### 3. 标注数据格式
支持的JSONB数据格式：
```json
{
    "shapes": [
        {"label": "主任务", "points": [...], ...},
        {"label": "XXX", "points": [...], ...}
    ]
}
```
或
```json
{
    "labels": ["主任务", "XXX", "YYY"]
}
```
或
```json
{
    "annotations": [
        {"label": "主任务", ...},
        {"label": "XXX", ...}
    ]
}
```

## UI控件配置指南

### 1. 必需控件
- **lineEdit_174**: 数据库名称
  - 用途: 指定要连接的PostgreSQL数据库名称
  - 验证: 不能为空
  
- **lineEdit_175**: 数据库表名
  - 用途: 指定要查询的数据表名称
  - 验证: 不能为空

### 2. 模式选择控件
- **rotate_146**: segment模式checkbox
  - 用途: 选择查询segmentation_data列
  - 行为: 与rotate_147互斥使用
  
- **rotate_147**: obb模式checkbox  
  - 用途: 选择查询obb_data列
  - 行为: 与rotate_146互斥使用

### 3. 可选控件
- **lineEdit_176**: 数据源筛选条件
  - 用途: 添加额外的WHERE条件筛选
  - 示例: "data_source = 'training_set'"
  - 验证: 可以为空

## 错误处理

### 1. 数据库连接错误
```python
try:
    result = deleter.execute_database_batch_deletion()
except ConnectionError as e:
    print(f"数据库连接失败: {e}")
```

### 2. 配置错误
```python
try:
    result = deleter.execute_database_batch_deletion()
except ValueError as e:
    print(f"配置错误: {e}")
```

### 3. 查询错误
系统会自动处理查询错误并在日志中记录详细信息。

## 性能优化建议

### 1. 数据库索引
```sql
-- 为image_path列创建索引以提高查询性能
CREATE INDEX idx_image_path ON your_table_name(image_path);

-- 如果使用data_source筛选，也为其创建索引
CREATE INDEX idx_data_source ON your_table_name(data_source);
```

### 2. 批量处理
- 系统会显示处理进度（每10个文件显示一次）
- 大量文件处理时建议分批执行

### 3. 连接管理
- 每次查询都会建立新的数据库连接
- 连接会在查询完成后自动关闭
- 异常情况下也会确保连接正确关闭

## 与传统模式的对比

| 特性 | 传统JSON模式 | 数据库模式 |
|------|-------------|-----------|
| 数据源 | JSON文件 | PostgreSQL数据库 |
| 查询效率 | 文件系统遍历 | SQL查询 |
| 数据一致性 | 依赖文件系统 | 数据库事务保证 |
| 扩展性 | 受文件系统限制 | 数据库级别扩展 |
| 配置复杂度 | 简单 | 中等 |
| 依赖项 | 无 | PostgreSQL + psycopg2 |

## 故障排除

### 1. 常见问题
- **数据库连接失败**: 检查PostgreSQL服务是否运行，连接参数是否正确
- **表不存在**: 确认数据库和表名称正确
- **没有数据返回**: 检查image_path匹配逻辑和数据源筛选条件
- **标签提取失败**: 验证JSONB数据格式是否符合要求

### 2. 调试技巧
- 启用详细日志记录查看具体错误信息
- 使用数据库客户端工具验证查询语句
- 检查UI控件的值是否正确设置

## 版本兼容性

- **向后兼容**: 完全兼容原有的JSON模式
- **新功能**: 数据库模式作为额外功能添加
- **迁移**: 无需修改现有代码，只需添加CheckBoxManager参数
