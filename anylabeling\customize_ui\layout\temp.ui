<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1586</width>
    <height>826</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QScrollArea" name="scrollArea">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>20</y>
     <width>391</width>
     <height>1200</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>1200</height>
    </size>
   </property>
   <property name="font">
    <font>
     <family>楷体</family>
     <pointsize>18</pointsize>
     <weight>50</weight>
     <bold>false</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QScrollArea {
                        border:4px solid rgb(143, 143, 143);
border-radius:6px;
                        }
                    </string>
   </property>
   <property name="frameShape">
    <enum>QFrame::StyledPanel</enum>
   </property>
   <property name="verticalScrollBarPolicy">
    <enum>Qt::ScrollBarAsNeeded</enum>
   </property>
   <property name="horizontalScrollBarPolicy">
    <enum>Qt::ScrollBarAlwaysOff</enum>
   </property>
   <property name="widgetResizable">
    <bool>false</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
   </property>
   <widget class="QWidget" name="scrollAreaWidgetContents">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>2477</width>
      <height>5000</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>5000</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="verticalLayoutWidget_3">
   <property name="geometry">
    <rect>
     <x>860</x>
     <y>200</y>
     <width>316</width>
     <height>51</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_6">
    <property name="spacing">
     <number>2</number>
    </property>
    <item>
     <widget class="QCheckBox" name="Translate_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <family>JetBrains Mono SemiBold</family>
        <pointsize>18</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="cursor">
       <cursorShape>OpenHandCursor</cursorShape>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="acceptDrops">
       <bool>false</bool>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox {
	spacing: 10px;
 font-family:&quot;JetBrains Mono SemiBold&quot;, &quot;Microsoft YaHei&quot;;
}

QCheckBox::indicator:hover {
        border-color: #3498db;  /* 鼠标悬停时的边框颜色 */
    }

QCheckBox::indicator:unchecked {
        width: 20px;
        height: 20px;
        border: 1px solid #999999;
        border-radius: 2px;
        background-color: white;
        
    }
QCheckBox::indicator:checked {
        width: 20px;
        height: 20px;
        border: 1px solid rgb(49, 49, 49);
        border-radius: 2px;
        background-color: green;
		color:rgb(0, 139, 0);
    }

QCheckBox::checked {
        
		color:rgb(0, 139, 0);
    }

</string>
      </property>
      <property name="text">
       <string>Shear</string>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
      <property name="autoRepeatDelay">
       <number>300</number>
      </property>
      <property name="tristate">
       <bool>false</bool>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_502">
      <property name="sizePolicy">
       <sizepolicy hsizetype="MinimumExpanding" vsizetype="Maximum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <family>仿宋</family>
        <pointsize>12</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
color: rgb(126, 126, 126);
}</string>
      </property>
      <property name="text">
       <string>百分比平移变换，基于图像尺寸的相对平移</string>
      </property>
      <property name="wordWrap">
       <bool>false</bool>
      </property>
      <property name="indent">
       <number>10</number>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="layoutWidget_2">
   <property name="geometry">
    <rect>
     <x>660</x>
     <y>70</y>
     <width>541</width>
     <height>47</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_515">
    <property name="leftMargin">
     <number>5</number>
    </property>
    <property name="rightMargin">
     <number>5</number>
    </property>
    <item>
     <widget class="QLabel" name="label_261">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>JetBrains Mono SemiBold</family>
        <pointsize>-1</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel {
spacing: 10px;
 font-family:&quot;JetBrains Mono SemiBold&quot;, &quot;Microsoft YaHei&quot;;
font-size: 18px;
}</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="text">
       <string>imgsz 输入图像尺寸</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_74">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="MinimumExpanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>45</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>45</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>JetBrains Mono SemiBold</family>
        <pointsize>14</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit {
                                                border: 2px solid #000; /* 可选：设置边框颜色和宽度 */
                                                border-radius: 5px; /* 可选：设置圆角半径 */
                                                padding-left:5px;
                                                }
                                            </string>
      </property>
      <property name="text">
       <string>640</string>
      </property>
      <property name="placeholderText">
       <string>val_batch 训练批次大小</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_262">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>450</y>
     <width>174</width>
     <height>25</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="font">
    <font>
     <family>JetBrains Mono SemiBold</family>
     <pointsize>-1</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">QLabel {
spacing: 10px;
 font-family:&quot;JetBrains Mono SemiBold&quot;, &quot;Microsoft YaHei&quot;;
font-size: 18px;
}</string>
   </property>
   <property name="frameShape">
    <enum>QFrame::NoFrame</enum>
   </property>
   <property name="text">
    <string>imgsz 输入图像尺寸</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
