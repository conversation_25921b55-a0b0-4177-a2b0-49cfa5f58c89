import json
import os
import threading
import traceback
from typing import Dict, List, Optional, Any

from PyQt5.QtWidgets import QLineEdit
from global_tools.ui_tools import (
	CheckBoxManager, QLabelManager,
	LineEditManager, LogOutput, constant,
)
from global_tools.utils import EnhancedProcess, SharedDataManager, ProcessLogger
from global_tools.utils import Logger, Colors

from anylabeling.customize_ui.helper.folder_importer import FolderImporter
from anylabeling.customize_ui.src.ui_operate.predict_image.condition_filter import ConditionFilter


def check_lineedit( line_edit_manager: LineEditManager ):
	# 校验 QLineEdit 的输入是否为 Windows 路径
	validator_path_qlienedit = [ "lineEdit_48", "lineEdit_63", "lineEdit_74" ]
	[
		line_edit_manager.set_input_validator( name=x, validator_type="regexp", pattern=constant.WINDOWS_PATH_REGEX )
		for x in validator_path_qlienedit
	]


# ====================================================================================================
# ====================================================================================================
# ====================================================================================================


class PredictImageProcess:

	def __process_segment_mode( self, results, image_path, shared_data_proxy ):
		"""
		处理segment模式的预测结果。

		该方法专门处理YOLO分割模式的预测结果，提取分割信息并格式化为标准输出格式。
		segments数据保持字典格式 {'x': [...], 'y': [...]}，与DetectionToJsonConverter兼容。

		Args:
			results: YOLO模型的预测结果对象
			image_path (str): 当前处理的图像文件路径
			shared_data_proxy: 共享数据代理对象，用于存储预测结果

		Returns:
			None: 结果直接添加到shared_data_proxy中

		Data Format:
			生成的segments数据格式为: {'x': [x1, x2, ...], 'y': [y1, y2, ...]}
			该格式与_create_polygon_annotation方法兼容，最终转换为points格式: [[x1,y1], [x2,y2], ...]

		Usage Example:
			```python
			# 在__call__方法中调用
			if prediction_mode == "segment":
				self.__process_segment_mode(results, image_path, shared_data_proxy)
			```
		"""
		try:
			summary = results[ 0 ].summary() if len( results[ 0 ] ) >= 1 else None
			if summary and len( summary ) >= 1:
				for item in summary:
					segments = item.get( "segments", { } )
					x = segments.get( "x", [ ] )
					y = segments.get( "y", [ ] )
					# 保持segments为字典格式 {'x': [...], 'y': [...]}，与_create_polygon_annotation方法兼容
					item[ "segments" ] = { "x": x, "y": y }
					item[ 'image_path' ] = image_path
					item[ 'speed' ] = results[ 0 ].speed

				if shared_data_proxy:
					shared_data_proxy.append_to_list( key="predict_results", value=summary )
		except Exception as e:
			# 记录segment模式处理异常，但不中断整个预测流程
			print( f"处理segment模式结果时发生错误: {e}" )

	def __process_obb_mode( self, results, image_path, shared_data_proxy ):
		"""
		处理obb模式的预测结果。

		该方法专门处理YOLO有向边界框模式的预测结果，计算旋转角度和四个角点坐标。

		Args:
			results: YOLO模型的预测结果对象
			image_path (str): 当前处理的图像文件路径
			shared_data_proxy: 共享数据代理对象，用于存储预测结果

		Returns:
			None: 结果直接添加到shared_data_proxy中

		Usage Example:
			```python
			# 在__call__方法中调用
			if prediction_mode == "obb":
				self.__process_obb_mode(results, image_path, shared_data_proxy)
			```
		"""
		import math

		try:
			summary = results[ 0 ].summary() if len( results[ 0 ] ) >= 1 else None
			if summary and len( summary ) >= 1:
				xywhr = results[ 0 ].obb.xywhr.tolist()
				for i in range( len( summary ) ):
					item = summary.pop( 0 )
					item[ 'image_path' ] = image_path
					item[ 'speed' ] = results[ 0 ].speed

					# 获取当前目标的xywhr数据
					x, y, w, h, r = xywhr[ i ]

					# 计算旋转后的四个角点坐标
					cos_r = math.cos( r )
					sin_r = math.sin( r )

					# 计算未旋转时的四个角点相对于中心点的偏移
					half_w = w / 2
					half_h = h / 2

					# 计算旋转后的四个角点坐标
					x1 = x + half_w * cos_r - half_h * sin_r
					y1 = y + half_w * sin_r + half_h * cos_r

					x2 = x - half_w * cos_r - half_h * sin_r
					y2 = y - half_w * sin_r + half_h * cos_r

					x3 = x - half_w * cos_r + half_h * sin_r
					y3 = y - half_w * sin_r - half_h * cos_r

					x4 = x + half_w * cos_r + half_h * sin_r
					y4 = y + half_w * sin_r - half_h * cos_r

					# 将四个角点坐标添加到结果中，保存为box列表格式 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
					# item[ 'box' ] = {
					# 	'x1': x1, 'y1': y1,
					# 	'x2': x2, 'y2': y2,
					# 	'x3': x3, 'y3': y3,
					# 	'x4': x4, 'y4': y4
					# }
					item[ "box" ] = [ [ x1, y1 ], [ x2, y2 ], [ x3, y3 ], [ x4, y4 ] ]
					item[ "direction" ] = r
					summary.append( item )

				if shared_data_proxy:
					shared_data_proxy.append_to_list( key="predict_results", value=summary )
		except Exception as e:
			# 记录obb模式处理异常，但不中断整个预测流程
			print( f"处理obb模式结果时发生错误: {e}" )

	def __call__(
			self, shared_data_proxy: SharedDataManager, data_queue, process_logger: ProcessLogger, *args, **kwargs
	) -> Any:
		from ultralytics import YOLO
		import cv2
		import time

		process_logger and process_logger.log(
			{
				"type":    "start",
				"message": "预测图像进程已启动成功"
			}
		)

		images_to_predict = kwargs.get( "images_to_predict", [ ] )
		model_path = kwargs.get( "model_path", None )
		images_size = kwargs.get( "images_size", 640 )
		prediction_mode = kwargs.get( "prediction_mode", "segment" )  # 获取预测模式，默认为segment

		# 根据预测模式初始化YOLO模型
		model = YOLO( model_path, task=prediction_mode )
		total_num = len( images_to_predict )

		# 记录使用的预测模式
		process_logger and process_logger.log(
			{
				"type":    "info",
				"message": f"使用预测模式: {prediction_mode}"
			}
		)

		process_logger and process_logger.log(
			{
				"type":      "progress_start",
				"total_num": [ 0, total_num ]
			}
		)

		try:
			for index, image_path in enumerate( images_to_predict ):
				"""
				[{
					'box': {'x1': 359.40427, 'x2': 593.74994, 'y1': 260.13669, 'y2': 423.41794}, 
					'class': 0, 
					'confidence': 0.91943, 
					'name': '主人物', 
					'segments': {'x': [359.2187194824219, 359.2187194824219, ...], 'y': [263.90625, 302.5, ...]}
				},...]
				"""
				img = cv2.imread( image_path )

				# 记录推理开始时间
				start_time = time.time()

				results = model(
					img,  # 使用原始彩色图像
					device='cuda:0',  # 使用GPU进行预测
					conf=0.5,
					iou=0.5,
					agnostic_nms=True,
					half=True,  # 是否使用半精度
					verbose=False,  # 是否显示详细信息
					imgsz=images_size  # 输入图像尺寸
				)

				# 记录推理结束时间并计算耗时（毫秒）
				end_time = time.time()
				inference_time = (end_time - start_time) * 1000

				# 根据预测模式调用对应的处理方法
				if prediction_mode == "obb":
					self.__process_obb_mode( results, image_path, shared_data_proxy )
				elif prediction_mode == "segment":
					self.__process_segment_mode( results, image_path, shared_data_proxy )
				else:
					# 未知模式时记录警告并使用默认segment处理
					print( f"警告: 未知的预测模式 '{prediction_mode}'，使用默认segment模式处理" )
					self.__process_segment_mode( results, image_path, shared_data_proxy )

				process_logger and process_logger.log(
					{
						"type":           "change_update",
						"current_num":    index + 1,
						"total_num":      total_num,
						"inference_time": f"{inference_time:.2f}"  # 将推理时间添加到日志中
					}
				)  # type: ignore
		except Exception as e:
			traceback.print_exc()

		process_logger and process_logger.log(
			{
				"type":    "end",
				"message": "预测图像进程已结束"
			}
		)


class ImagePredictor:
	"""
	一个用于执行图像预测任务的健壮类。

	该类封装了从UI获取输入、验证路径、过滤未标注图像以及执行预测（当前为模拟）的全部逻辑。
	它被设计为在后台线程中运行，以避免阻塞用户界面，并提供详细的日志反馈。
	"""
	enhanced_process: EnhancedProcess
	predict_results: List

	# 预测模式配置常量 - 避免硬编码，便于维护和扩展
	__PREDICTION_MODE_CONFIG = {
		"segment_checkbox": {
			"object_name": "rotate_40",
			"mode_name":   "segment",
			"description": "YOLO分割模式 - 用于生成精确的对象轮廓分割"
		},
		"obb_checkbox":     {
			"object_name": "rotate_41",
			"mode_name":   "obb",
			"description": "YOLO有向边界框模式 - 用于检测旋转对象的精确边界框"
		}
	}

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			label_manager: QLabelManager,
			log_output: LogOutput,
			logger: Logger,
			checkbox_manager: CheckBoxManager,
	):
		"""
		初始化 ImagePredictor 实例。

		Args:
			line_edit_manager (LineEditManager): 用于与UI中的 QLineEdit 控件交互的管理器。
			label_manager (QLabelManager): 用于与UI中的 QLabel 控件交互的管理器。
			log_output (LogOutput): 用于向UI日志窗口发送富文本消息的实例。
			logger (Logger): 用于向控制台输出标准日志的实例。
			checkbox_manager (CheckBoxManager): 用于管理复选框状态的管理器实例。
		"""
		self.__line_edit_manager = line_edit_manager
		self.__label_manager = label_manager
		self.__log_output = log_output
		self.__checkbox_manager = checkbox_manager
		self.__logger = logger
		self._stop_event = threading.Event()

		# PyQt5控件统一映射配置字典
		# 建立语义化的控件访问接口，便于后续修改和扩展
		self.__WIDGET_MAPPING = {
			# LineEdit控件 - 用户输入相关
			"line_edits": {
				"image_folder_path": {
					"widget_id":   "lineEdit_48",
					"widget_type": "QLineEdit",
					"description": "图像文件夹路径输入框 - 用于指定包含待预测图像的文件夹路径",
					"function":    "获取用户输入的图像文件夹路径，系统将从该路径读取所有支持格式的图像文件进行预测"
				},
				"model_file_path":   {
					"widget_id":   "lineEdit_63",
					"widget_type": "QLineEdit",
					"description": "模型文件路径输入框 - 用于指定YOLO模型文件的路径",
					"function":    "获取用户输入的模型文件路径，系统将加载该模型进行图像预测和分割任务"
				},
				"image_size":        {
					"widget_id":   "lineEdit_74",
					"widget_type": "QLineEdit",
					"description": "图像尺寸输入框 - 用于指定模型推理时的图像尺寸",
					"function":    "获取用户输入的图像尺寸参数，控制模型推理时的输入图像大小，影响预测精度和速度"
				}
			},

			# Label控件 - 状态显示相关
			"labels":     {
				"inference_status_display": {
					"widget_id":   "label_182",
					"widget_type": "QLabel",
					"description": "推理状态显示标签 - 实时显示当前推理任务的状态信息",
					"function":    "显示推理过程的状态信息，如'开始推理...'、'推理完成'等，为用户提供实时的任务状态反馈"
				},
				"progress_display":         {
					"widget_id":   "label_587",
					"widget_type": "QLabel",
					"description": "进度显示标签 - 实时显示图像预测的进度信息",
					"function":    "显示预测进度，格式为'当前数量/总数量 (百分比%)'，为用户提供实时的预测进度反馈"
				}
			}
		}

	def __get_prediction_mode( self ) -> str:
		"""
		动态检查复选框状态并返回对应的预测模式。

		根据CheckBoxManager检查复选框状态：
		- 如果rotate_40复选框被选中 → 返回'segment'模式
		- 如果rotate_41复选框被选中 → 返回'obb'模式
		- 如果都未选中或都选中 → 返回默认'segment'模式

		Returns:
			str: 预测模式 ('segment' 或 'obb')

		Usage Example:
			```python
			mode = self.__get_prediction_mode()
			if mode == "segment":
				print("使用分割模式进行预测")
			elif mode == "obb":
				print("使用有向边界框模式进行预测")
			```
		"""
		try:
			if not self.__checkbox_manager:
				self.__log_warning( "CheckBoxManager不可用，使用默认segment模式" )
				return "segment"

			# 获取segment模式复选框状态
			segment_config = self.__PREDICTION_MODE_CONFIG[ "segment_checkbox" ]
			segment_checked = self.__checkbox_manager.get_checked_state_by_object_name(
				segment_config[ "object_name" ]
			)

			# 获取obb模式复选框状态
			obb_config = self.__PREDICTION_MODE_CONFIG[ "obb_checkbox" ]
			obb_checked = self.__checkbox_manager.get_checked_state_by_object_name(
				obb_config[ "object_name" ]
			)

			# 记录复选框状态（调试信息）
			if self.__logger:
				self.__logger.debug(
					f"预测模式检查 - {segment_config[ 'object_name' ]}(segment): {segment_checked}, "
					f"{obb_config[ 'object_name' ]}(obb): {obb_checked}"
				)

			# 决策逻辑：优先级 obb > segment > 默认segment
			if obb_checked is True:
				selected_mode = obb_config[ "mode_name" ]
				mode_description = obb_config[ "description" ]
			elif segment_checked is True:
				selected_mode = segment_config[ "mode_name" ]
				mode_description = segment_config[ "description" ]
			else:
				# 都未选中时使用默认segment模式
				selected_mode = segment_config[ "mode_name" ]
				mode_description = f"{segment_config[ 'description' ]} (默认)"
				if self.__logger:
					self.__logger.info( "未检测到明确的模式选择，使用默认segment模式" )

			# 记录选择的模式
			self.__log_info( f"预测模式: {selected_mode} - {mode_description}" )

			return selected_mode

		except Exception as e:
			self.__log_error( f"获取预测模式时发生错误: {e}" )
			# 出错时返回安全的默认模式
			return "segment"

	def __get_widget_value( self, semantic_name: str ) -> str:
		"""
		根据语义化名称获取控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			str: 控件的文本值，如果控件不存在则返回空字符串

		Usage Example:
			```python
			# 获取图像文件夹路径
			image_path = self.__get_widget_value("image_folder_path")

			# 获取模型文件路径
			model_path = self.__get_widget_value("model_file_path")

			# 获取图像尺寸
			image_size = self.__get_widget_value("image_size")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[ semantic_name ]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning( f"未找到语义化名称 '{semantic_name}' 对应的控件映射" )
				return ""

			widget_id = widget_config[ "widget_id" ]
			widget_type = widget_config[ "widget_type" ]

			# 根据控件类型获取值
			if widget_type == "QLineEdit":
				text_value = self.__line_edit_manager.get_text( widget_id )
				return text_value if text_value is not None else ""
			else:
				if self.__logger:
					self.__logger.warning( f"控件类型 '{widget_type}' 不支持获取文本值" )
				return ""

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取控件 '{semantic_name}' 的值时发生错误: {e}" )
			return ""

	def __get_line_edit_widget( self, semantic_name: str ):
		"""
		根据语义化名称获取LineEdit控件对象。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			QLineEdit: LineEdit控件对象，如果不存在则返回None

		Usage Example:
			```python
			# 获取图像文件夹路径输入框
			image_folder_widget = self.__get_line_edit_widget("image_folder_path")

			# 获取模型文件路径输入框
			model_path_widget = self.__get_line_edit_widget("model_file_path")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[ semantic_name ]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning( f"未找到语义化名称 '{semantic_name}' 对应的控件映射" )
				return None

			widget_id = widget_config[ "widget_id" ]
			widget_type = widget_config[ "widget_type" ]

			# 根据控件类型获取控件对象
			if widget_type == "QLineEdit":
				return self.__line_edit_manager.get_line_edit( name=widget_id )
			else:
				if self.__logger:
					self.__logger.warning( f"控件类型 '{widget_type}' 不是LineEdit" )
				return None

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取控件 '{semantic_name}' 的对象时发生错误: {e}" )
			return None

	def __update_label_text( self, semantic_name: str, text: str ) -> bool:
		"""
		根据语义化名称更新标签的文本显示。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			text (str): 要设置的文本内容

		Returns:
			bool: 更新成功返回True，失败返回False

		Usage Example:
			```python
			# 更新推理状态显示
			success = self.__update_label_text("inference_status_display", "开始推理...")

			# 更新进度显示
			success = self.__update_label_text("progress_display", "50/100 (50.0%)")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[ semantic_name ]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning( f"未找到语义化名称 '{semantic_name}' 对应的控件映射" )
				return False

			widget_id = widget_config[ "widget_id" ]
			widget_type = widget_config[ "widget_type" ]

			# 根据控件类型更新文本
			if widget_type == "QLabel":
				if self.__label_manager:
					self.__label_manager.set_text( label_name=widget_id, text=text )
					return True
				else:
					if self.__logger:
						self.__logger.warning( "标签管理器不可用" )
					return False
			else:
				if self.__logger:
					self.__logger.warning( f"控件类型 '{widget_type}' 不是标签" )
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"更新标签 '{semantic_name}' 的文本时发生错误: {e}" )
			return False

	def __update_label_stylesheet( self, semantic_name: str, stylesheet: str ) -> bool:
		"""
		根据语义化名称更新标签的样式。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			stylesheet (str): 要设置的样式字符串

		Returns:
			bool: 更新成功返回True，失败返回False

		Usage Example:
			```python
			# 更新推理状态显示样式
			success = self.__update_label_stylesheet("inference_status_display", "color: green; font-size: 18;")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[ semantic_name ]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning( f"未找到语义化名称 '{semantic_name}' 对应的控件映射" )
				return False

			widget_id = widget_config[ "widget_id" ]
			widget_type = widget_config[ "widget_type" ]

			# 根据控件类型更新样式
			if widget_type == "QLabel":
				if self.__label_manager:
					self.__label_manager.set_stylesheet( label_name=widget_id, stylesheet=stylesheet )
					return True
				else:
					if self.__logger:
						self.__logger.warning( "标签管理器不可用" )
					return False
			else:
				if self.__logger:
					self.__logger.warning( f"控件类型 '{widget_type}' 不是标签" )
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"更新标签 '{semantic_name}' 的样式时发生错误: {e}" )
			return False

	def __get_widget_info( self, semantic_name: str ) -> Optional[ dict ]:
		"""
		获取控件的详细配置信息。

		Args:
			semantic_name (str): 控件的语义化名称

		Returns:
			Optional[dict]: 控件的配置信息字典，如果不存在则返回None

		Usage Example:
			```python
			# 获取控件配置信息
			info = self.__get_widget_info("image_folder_path")
			if info:
				print(f"控件ID: {info['widget_id']}")
				print(f"控件类型: {info['widget_type']}")
				print(f"功能描述: {info['description']}")
			```
		"""
		try:
			# 查找控件配置
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					return widgets[ semantic_name ].copy()
			return None

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取控件 '{semantic_name}' 的信息时发生错误: {e}" )
			return None

	def __log_info( self, message: str ):
		"""记录一条信息级别的日志，同时输出到控制台和UI。"""
		self.__logger.info( message )
		self.__log_output.append( message, color=Colors.INFO )

	def __log_warning( self, message: str ):
		"""记录一条警告级别的日志，同时输出到控制台和UI。"""
		self.__logger.warning( message )
		self.__log_output.append( message, color=Colors.WARNING )

	def __log_error( self, message: str ):
		"""记录一条错误级别的日志，同时输出到控制台和UI。"""
		self.__logger.error( message )
		self.__log_output.append( message, color=Colors.ERROR )

	def __log_success( self, message: str ):
		"""记录一条成功级别的日志，同时输出到控制台和UI。"""
		self.__logger.info( message )
		self.__log_output.append( message, color=Colors.SUCCESS )

	def __find_all_images( self, image_dir: str ) -> List[ str ]:
		"""
		在指定目录中高效地查找所有图像文件。

		此方法使用`os.scandir()`进行性能优化，以最小的系统调用次数遍历目录。
		它会返回一个包含所有支持格式（.jpg, .jpeg, .png, .bmp, .gif）图像绝对路径的列表。

		Args:
			image_dir (str): 要搜索的图像文件夹的绝对路径。

		Returns:
			List[str]: 一个包含所有图像绝对路径的列表。
		"""
		# 日志优化：移除详细的搜索开始信息，只在最后显示结果
		image_extensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" }
		image_paths = [ ]
		try:
			for entry in os.scandir( image_dir ):
				if entry.is_file():
					_name, ext = os.path.splitext( entry.name )
					if ext.lower() in image_extensions:
						image_paths.append( entry.path )
		except OSError as e:
			self.__log_error( f"无法扫描目录 '{image_dir}': {e}" )
			return [ ]

		# 日志优化：简化为一条简洁的结果信息
		self.__log_info( f"发现 {len( image_paths )} 张图像文件" )
		return image_paths

	def predict( self, condition_string: str ) -> None:
		"""
		执行图像预测的主方法。
		此方法应该在一个单独的线程中被调用。
		"""
		self.condition_string = condition_string

		self._stop_event.clear()
		try:
			# 使用统一的控件映射接口获取控件对象和值
			image_dir_le = self.__get_line_edit_widget( "image_folder_path" )
			model_path_le = self.__get_line_edit_widget( "model_file_path" )
			images_size = self.__get_widget_value( "image_size" )

			if not image_dir_le:
				self.__log_error( "错误：无法找到图像文件夹路径输入框。" )
				return
			if not model_path_le:
				self.__log_error( "错误：无法找到模型文件路径输入框。" )
				return

			image_dir = image_dir_le.text()
			model_path = model_path_le.text()

			# 日志优化：合并路径信息为一条简洁的配置确认信息
			self.__log_info(
				f"配置确认 - 图像目录: {os.path.basename( image_dir )}, 模型: {os.path.basename( model_path )}"
			)

			if not os.path.isdir( image_dir ):
				self.__log_error( f"错误：图像文件夹路径不存在或不是一个目录 -> {image_dir}" )
				return

			if not os.path.isfile( model_path ):
				self.__log_error( f"错误：模型文件路径不存在或不是一个文件 -> {model_path}" )
				return

			images_to_predict = self.__find_all_images( image_dir )

			# 获取动态预测模式
			prediction_mode = self.__get_prediction_mode()

			self.enhanced_process = EnhancedProcess( target=PredictImageProcess(), use_shared_queue_manager=True )
			self.enhanced_process.set_log_callback( self.log_callback )
			self.enhanced_process.start(
				images_to_predict=images_to_predict,
				model_path=model_path,
				images_size=int( images_size ),
				prediction_mode=prediction_mode
			)
			self.enhanced_process.wait_for_completion()
			self.predict_results = self.enhanced_process.get_shared_data_proxy().get_list(  # type: ignore
				key="predict_results"
			)  # type: ignore
			self.enhanced_process.shutdown_manager()
			self.__log_output.append(
				message=f"预测完成，预期 {len( images_to_predict )} 张图像，实际找到有效 {len( self.predict_results )} 张图像",
				color=Colors.SUCCESS
			)

			# 保存预测结果到临时JSON文件
			import json
			temp_file_path = os.path.join( os.path.dirname( os.path.abspath( __file__ ) ), "temp.json" )

			try:
				with open( temp_file_path, "w", encoding="utf-8" ) as f:
					json.dump( self.predict_results, f, ensure_ascii=False, indent=4 )
			# 日志优化：移除临时文件路径详情，用户不需要关心临时文件位置
			except Exception as save_error:
				self.__log_error( f"保存预测结果到临时文件时出错: {save_error}" )

		except Exception as e:
			self.__log_error( f"预测过程中发生未预料的错误: {e}" )
			import traceback
			self.__logger.error( traceback.format_exc() )

	async def log_callback( self, log_entry: Dict[ str, Any ] ):
		# 接收日志信息回调函数
		if isinstance( log_entry, dict ) and log_entry.get( "type" ) == "start":
			self.__log_info( log_entry.get( "message" ) )
			self.__update_label_text( "inference_status_display", "开始推理..." )
			self.__update_label_stylesheet(
				"inference_status_display", f"color: {Colors.PRIMARY}; font-size: 14;"
			)
		elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "info":
			# 处理信息类型日志（包括预测模式信息）
			self.__log_info( log_entry.get( "message" ) )
		elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "end":
			self.__log_info( log_entry.get( "message" ) )
			self.__update_label_text( "inference_status_display", "推理完成" )
			self.__update_label_stylesheet( "inference_status_display", "color: green; font-size: 18;" )
		elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "progress_start":
			# 不再需要设置进度条范围
			pass
		elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "change_update":
			current_num = log_entry.get( "current_num", 0 )
			total_num = log_entry.get( "total_num", 0 )
			# 日志优化：移除inference_time的获取，因为不再在日志中显示详细推理时间

			# 计算进度百分比
			progress_percent = (current_num / total_num * 100) if total_num > 0 else 0

			# 使用统一的控件映射接口更新进度显示
			self.__update_label_text(
				"progress_display", f"{current_num}/{total_num} ({progress_percent:.1f}%)"
			)

			# 日志优化：只在关键进度节点显示日志，减少频繁输出
			# 在10%、25%、50%、75%、100%等关键节点显示进度
			if current_num == total_num or progress_percent in [ 10, 25, 50, 75 ] or current_num % max(
					1, total_num // 10
			) == 0:
				self.__log_info(
					f"预测进度: {current_num}/{total_num} ({progress_percent:.1f}%)"
				)

	@property
	def get_predict_results( self ) -> List:
		return self.predict_results

	def handle_predict_results( self, condition_string: str ):
		"""
		处理预测结果，应用条件筛选。

		Args:
			condition_string: 用于筛选数据的条件字符串

		Returns:
			筛选后的结果列表
		"""
		temp_file_path = os.path.join( os.path.dirname( os.path.abspath( __file__ ) ), "temp.json" )
		# 读取临时文件中的预测结果
		try:
			if os.path.exists( temp_file_path ):
				with open( temp_file_path, 'r', encoding='utf-8' ) as f:
					self.predict_results = json.load( f )
				# 日志优化：简化临时文件加载信息
				self.__log_info( f"加载了 {len( self.predict_results )} 项预测结果" )
			else:
				self.__log_info( "使用内存中的预测结果" )
		except Exception as e:
			self.__log_error( f"读取临时文件时出错: {e}" )
			self.__logger.error( f"读取临时文件错误详情: {str( e )}" )

		results = self.predict_results

		# 日志优化：合并筛选过程的多条信息为一条简洁的开始信息
		self.__log_info( f"开始筛选 {len( results )} 项预测结果（条件: {condition_string}）" )

		# 使用存储的条件字符串创建筛选器，并传递日志组件
		condition_filter = ConditionFilter( condition_string, self.__log_output, self.__logger, self.__label_manager )

		# 应用筛选器并返回结果，使用单项筛选模式
		filtered_results = condition_filter.filter_data( results, filter_individual_items=True )

		filtered_ratio = (len( filtered_results ) / len( results ) * 100) if results else 0
		self.__log_success(
			f"筛选完成，保留了 {len( filtered_results )}/{len( results )} 项结果 ({filtered_ratio:.1f}%)"
		)

		# 这里可以添加额外的处理逻辑...

		try:
			detection_to_json_converter = DetectionToJsonConverter(
				log_output=self.__log_output,
				logger=self.__logger,
				line_edit_manager=self.__line_edit_manager,
				checkbox_manager=self.__checkbox_manager
			)
			_, converted_image_paths = detection_to_json_converter.convert( filtered_results )

			FolderImporter.load_from_path( lambda: converted_image_paths )
		except Exception as e:
			traceback.print_exc()

		return filtered_results

	def stop( self ):
		# 停止预测进程
		flag = self.enhanced_process.terminate_gracefully( timeout=10 )
		if flag:
			self.__log_output.append( message="预测进程已停止", color=Colors.SUCCESS )
			self.predict_results = self.enhanced_process.get_shared_data_proxy().get_list(  # type: ignore
				key="predict_results"
			)  # type: ignore
			self.enhanced_process.shutdown_manager()
			self.__log_output.append(
				message=f"预测完成，找到有效 {len( self.predict_results )} 张图像", color=Colors.SUCCESS
			)


class DetectionToJsonConverter:
	"""
	将检测结果转换为AnLabeling标注格式并保存为JSON文件

	该类负责将YOLO模型输出的检测结果（包括边界框和分割掩码）转换为
	AnLabeling工具可识别的JSON格式，并保存到对应图像的同一目录下。

	转换过程会处理以下元素：
	- 将segments（分割掩码点）转换为polygon类型的标注
	- 将box（边界框）转换为rectangle类型的标注
	- 将box（OBB格式边界框）转换为rotation类型的标注
	- 保留标签名称、置信度等信息
	- 使用原图像路径提取文件名和保存位置信息

	示例：
	```python
	# 创建转换器
	converter = DetectionToJsonConverter()

	# 转换检测结果并保存JSON文件
	converter.convert(detection_results)
	```
	"""

	# 类变量：默认缩放中心点类型，所有实例共享
	# 可以通过类方法动态设置，影响所有实例的缩放行为
	_default_scaling_center_type = "center"

	def __init__( self, version="3.0.3", log_output=None, logger=None, line_edit_manager=None, checkbox_manager=None ):
		"""
		初始化转换器

		Args:
			version: JSON格式的版本号，默认为"3.0.3"
			log_output: 用于在UI界面显示日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			line_edit_manager: LineEditManager实例，用于管理QLineEdit控件，可选
			checkbox_manager: CheckBoxManager实例，用于管理QCheckBox控件，可选
		"""
		self.version = version
		self.__log_output = log_output
		self.__logger = logger
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager

		# 动态标注类型选择功能的配置化控件映射，避免硬编码
		self.__ANNOTATION_TYPE_WIDGET_CONFIG = {
			"annotation_type_controls": {
				"segment_annotation_checkbox": {
					"widget_id":     "rotate_38",
					"widget_type":   "QCheckBox",
					"description":   "控制是否创建多边形(segment)标注的复选框",
					"default_value": True,
					"annotation_type": "polygon"
				},
				"obb_annotation_checkbox": {
					"widget_id":     "rotate_39",
					"widget_type":   "QCheckBox",
					"description":   "控制是否创建OBB(定向边界框)标注的复选框",
					"default_value": True,
					"annotation_type": "rotation"
				}
			}
		}

		# 标注框缩放功能的配置化控件映射，避免硬编码
		self.__SCALING_WIDGET_CONFIG = {
			"scaling_controls": {
				"width_scale_input":       {
					"widget_id":     "lineEdit_80",
					"widget_type":   "QLineEdit",
					"description":   "宽度缩放百分比输入框（如1.2表示120%）",
					"default_value": "1.0",
					"validation":    "float_positive"
				},
				"height_scale_input":      {
					"widget_id":     "lineEdit_84",
					"widget_type":   "QLineEdit",
					"description":   "高度缩放百分比输入框（如1.2表示120%）",
					"default_value": "1.0",
					"validation":    "float_positive"
				},
				"scaling_enable_checkbox": {
					"widget_id":     "rotate_30",
					"widget_type":   "QCheckBox",
					"description":   "标注框缩放开关复选框",
					"default_value": False
				}
			}
		}

		# 支持的缩放中心点类型配置
		# 每种类型定义了缩放时的参考点，影响标注框的变形方式
		self.__SCALING_CENTER_TYPES = {
			"center":      {
				"name":        "中心点缩放（默认）",
				"description": "以标注框的几何中心为参考点进行缩放，保持标注框相对位置不变",
				"algorithm":   "计算四个顶点的平均坐标作为中心点",
				"use_case":    "适用于大多数场景，保持标注框的整体平衡"
			},
			"leftTop":     {
				"name":        "左上角缩放",
				"description": "以标注框的左上角为固定点进行缩放，左上角位置保持不变",
				"algorithm":   "使用最小X坐标和最小Y坐标的交点作为参考点",
				"use_case":    "适用于需要保持左上角位置固定的场景"
			},
			"rightTop":    {
				"name":        "右上角缩放",
				"description": "以标注框的右上角为固定点进行缩放，右上角位置保持不变",
				"algorithm":   "使用最大X坐标和最小Y坐标的交点作为参考点",
				"use_case":    "适用于需要保持右上角位置固定的场景"
			},
			"leftBottom":  {
				"name":        "左下角缩放",
				"description": "以标注框的左下角为固定点进行缩放，左下角位置保持不变",
				"algorithm":   "使用最小X坐标和最大Y坐标的交点作为参考点",
				"use_case":    "适用于需要保持左下角位置固定的场景"
			},
			"rightBottom": {
				"name":        "右下角缩放",
				"description": "以标注框的右下角为固定点进行缩放，右下角位置保持不变",
				"algorithm":   "使用最大X坐标和最大Y坐标的交点作为参考点",
				"use_case":    "适用于需要保持右下角位置固定的场景"
			}
		}

	def _get_annotation_type_config( self, config_name: str ):
		"""
		获取标注类型控件配置信息

		Args:
			config_name (str): 配置名称，如 'segment_annotation_checkbox' 或 'obb_annotation_checkbox'

		Returns:
			dict: 控件配置信息，包含widget_id、widget_type、description等
			None: 如果配置不存在

		Usage Example:
			```python
			# 获取segment标注复选框配置
			segment_config = self._get_annotation_type_config('segment_annotation_checkbox')
			if segment_config:
				widget_id = segment_config['widget_id']  # 'rotate_38'
				description = segment_config['description']
			```
		"""
		try:
			return self.__ANNOTATION_TYPE_WIDGET_CONFIG["annotation_type_controls"].get(config_name, None)
		except (KeyError, AttributeError):
			if self.__logger:
				self.__logger.warning(f"无法获取标注类型配置: {config_name}")
			return None

	def _get_checkbox_state( self, config_name: str ):
		"""
		通过配置名称获取复选框的选中状态

		Args:
			config_name (str): 配置名称，如 'segment_annotation_checkbox' 或 'obb_annotation_checkbox'

		Returns:
			bool: 复选框的选中状态，True表示选中，False表示未选中
			None: 如果CheckBoxManager未初始化或复选框不存在

		Usage Example:
			```python
			# 检查是否启用segment标注
			segment_enabled = self._get_checkbox_state('segment_annotation_checkbox')
			if segment_enabled is True:
				# 创建多边形标注
				pass
			elif segment_enabled is False:
				# 跳过多边形标注
				pass
			else:
				# CheckBoxManager未初始化，使用默认值
				pass
			```
		"""
		try:
			# 检查CheckBoxManager是否可用
			if not self.__checkbox_manager:
				if self.__logger:
					self.__logger.debug("CheckBoxManager未初始化，无法获取复选框状态")
				return None

			# 获取控件配置
			config = self._get_annotation_type_config(config_name)
			if not config:
				if self.__logger:
					self.__logger.warning(f"未找到配置: {config_name}")
				return None

			# 获取控件ID
			widget_id = config.get('widget_id')
			if not widget_id:
				if self.__logger:
					self.__logger.warning(f"配置 {config_name} 缺少widget_id")
				return None

			# 通过CheckBoxManager获取复选框状态
			state = self.__checkbox_manager.get_checked_state_by_object_name(widget_id)

			if self.__logger:
				self.__logger.debug(f"复选框 {widget_id} ({config_name}) 状态: {state}")

			return state

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取复选框状态时发生错误: {str(e)}")
			return None

	def _get_scaling_config( self, config_name: str ):
		"""
		获取缩放控件配置信息

		Args:
			config_name: 配置名称（如"width_scale_input"）

		Returns:
			dict: 控件配置信息，如果不存在则返回None
		"""
		try:
			return self.__SCALING_WIDGET_CONFIG[ "scaling_controls" ].get( config_name )
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"获取缩放配置 '{config_name}' 时出错: {e}" )
			return None

	def _get_scaling_parameters( self ):
		"""
		从UI控件获取缩放参数

		Returns:
			tuple: (is_enabled, width_scale, height_scale)
				   is_enabled: 是否启用缩放
				   width_scale: 宽度缩放比例
				   height_scale: 高度缩放比例
		"""
		try:
			# 检查是否启用缩放功能
			if not self.__checkbox_manager:
				return False, 1.0, 1.0

			checkbox_config = self._get_scaling_config( "scaling_enable_checkbox" )
			if not checkbox_config:
				return False, 1.0, 1.0

			is_enabled = self.__checkbox_manager.get_checked_state_by_object_name(
				checkbox_config[ "widget_id" ]
			)

			if not is_enabled:
				return False, 1.0, 1.0

			# 获取缩放比例
			width_scale = 1.0
			height_scale = 1.0

			if self.__line_edit_manager:
				# 获取宽度缩放比例
				width_config = self._get_scaling_config( "width_scale_input" )
				if width_config:
					width_text = self.__line_edit_manager.get_text( width_config[ "widget_id" ] )
					try:
						width_scale = float( width_text ) if width_text else 1.0
						if width_scale <= 0:
							width_scale = 1.0
					except ValueError:
						width_scale = 1.0

				# 获取高度缩放比例
				height_config = self._get_scaling_config( "height_scale_input" )
				if height_config:
					height_text = self.__line_edit_manager.get_text( height_config[ "widget_id" ] )
					try:
						height_scale = float( height_text ) if height_text else 1.0
						if height_scale <= 0:
							height_scale = 1.0
					except ValueError:
						height_scale = 1.0

			return is_enabled, width_scale, height_scale

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取缩放参数时出错: {e}" )
			return False, 1.0, 1.0

	def _calculate_obb_center( self, points ):
		"""
		计算OBB标注框的几何中心点（优化版本）

		使用数值稳定的质心计算算法，提升精度和鲁棒性：
		- 增强输入验证和错误处理
		- 优化浮点运算，减少累积误差
		- 提供详细的调试信息

		数学模型：质心 = (Σx_i/n, Σy_i/n)
		其中 n=4 为OBB的顶点数量

		Args:
			points: OBB四个顶点坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

		Returns:
			tuple: (center_x, center_y) 几何中心点坐标

		Raises:
			无异常抛出，错误情况返回(0, 0)

		Performance:
			时间复杂度: O(1) - 常数时间
			空间复杂度: O(1) - 常数空间
			数值精度: 使用Kahan求和算法减少累积误差
		"""
		try:
			# 输入验证
			if not points or len( points ) != 4:
				if self.__logger:
					self.__logger.warning(
						f"计算OBB中心点: 无效的点数量，期望4个点，实际{len( points ) if points else 0}个点"
					)
				return 0.0, 0.0

			# 验证坐标数据的有效性
			valid_points = [ ]
			for i, point in enumerate( points ):
				try:
					x, y = float( point[ 0 ] ), float( point[ 1 ] )
					if not (abs( x ) < 1e15 and abs( y ) < 1e15):  # 检查数值范围
						if self.__logger:
							self.__logger.warning( f"计算OBB中心点: 点{i}坐标值过大 ({x}, {y})" )
						return 0.0, 0.0
					valid_points.append( (x, y) )
				except (ValueError, TypeError, IndexError):
					if self.__logger:
						self.__logger.warning( f"计算OBB中心点: 点{i}坐标格式无效 {point}" )
					return 0.0, 0.0

			# 使用优化的求和算法计算质心
			# 方法1：直接求和（对于4个点，精度足够）
			sum_x = sum( point[ 0 ] for point in valid_points )
			sum_y = sum( point[ 1 ] for point in valid_points )

			# 计算几何中心（质心）
			center_x = sum_x * 0.25  # 除以4，使用乘法避免除法运算
			center_y = sum_y * 0.25

			# 可选：验证计算结果的合理性（调试模式）
			if self.__logger:
				try:
					# 验证计算结果的合理性
					coord_range_x = max( point[ 0 ] for point in valid_points ) - min(
						point[ 0 ] for point in valid_points
					)
					coord_range_y = max( point[ 1 ] for point in valid_points ) - min(
						point[ 1 ] for point in valid_points
					)
					self.__logger.debug(
						f"OBB中心点计算: 中心=({center_x:.6f}, {center_y:.6f}), "
						f"X范围={coord_range_x:.2f}, Y范围={coord_range_y:.2f}"
					)
				except Exception:
					# 如果日志记录失败，静默忽略
					pass

			return center_x, center_y

		except Exception as e:
			# 增强的错误处理
			if self.__logger:
				self.__logger.warning( f"计算OBB中心点时发生未知错误: {e}" )
				import traceback
				self.__logger.debug( traceback.format_exc() )
			return 0.0, 0.0

	def get_available_scaling_center_types( self ):
		"""
		获取所有可用的缩放中心点类型

		Returns:
			dict: 所有可用的缩放中心点类型配置
		"""
		try:
			return self.__SCALING_CENTER_TYPES.copy()
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取可用缩放中心点类型时出错: {e}" )
			return { }

	@classmethod
	def set_default_scaling_center_type( cls, center_type: str ) -> bool:
		"""
		动态设置默认的缩放中心点类型（类方法）

		该类方法允许用户在运行时改变默认的缩放中心点类型，影响所有实例的缩放操作行为。
		设置成功后，所有 DetectionToJsonConverter 实例的缩放操作都将使用新的默认中心点类型。

		Args:
			center_type (str): 新的默认缩放中心点类型，必须是以下值之一：
				- "center": 中心点缩放（默认），以标注框几何中心为参考点
				- "leftTop": 左上角缩放，以左上角为固定点进行缩放
				- "rightTop": 右上角缩放，以右上角为固定点进行缩放
				- "leftBottom": 左下角缩放，以左下角为固定点进行缩放
				- "rightBottom": 右下角缩放，以右下角为固定点进行缩放

		Returns:
			bool: 设置成功返回True，失败返回False

		Raises:
			无异常抛出，所有错误都通过返回值处理

		Examples:
			>>> # 直接通过类调用，影响所有实例
			>>> success = DetectionToJsonConverter.set_default_scaling_center_type("leftTop")
			>>> if success:
			...     print("默认中心点类型已设置为左上角缩放")
			>>>
			>>> # 设置无效的中心点类型
			>>> success = DetectionToJsonConverter.set_default_scaling_center_type("invalid_type")
			>>> if not success:
			...     print("设置失败，无效的中心点类型")
			>>>
			>>> # 创建实例后，会使用新的默认中心点类型
			>>> converter = DetectionToJsonConverter(...)
			>>> converter.convert(detection_results)  # 使用leftTop中心点

		Notes:
			- 该方法是类方法，影响所有实例的行为
			- 设置后立即生效，影响后续所有实例的缩放操作
			- 如果传入无效的中心点类型，将保持原有设置不变
			- 建议在创建实例前设置，以确保一致的缩放行为
			- 可以通过 get_available_scaling_center_types() 方法查看所有可用类型

		Version:
			Added in v1.0.0 - 支持动态设置默认缩放中心点类型
		"""
		try:
			# 参数类型检查
			if not isinstance( center_type, str ):
				return False

			# 空值检查
			if not center_type or not center_type.strip():
				return False

			# 去除首尾空格
			center_type = center_type.strip()

			# 定义可用的缩放中心点类型（与实例中的配置保持一致）
			available_types = {
				"center":      "中心点缩放（默认）",
				"leftTop":     "左上角缩放",
				"rightTop":    "右上角缩放",
				"leftBottom":  "左下角缩放",
				"rightBottom": "右下角缩放"
			}

			# 配置验证：检查是否在可用类型中
			if center_type not in available_types:
				return False

			# 获取旧的默认类型
			old_center_type = cls._default_scaling_center_type

			# 更新类变量：默认中心点类型
			cls._default_scaling_center_type = center_type

			return True

		except Exception as e:
			# 异常处理：确保方法的健壮性
			return False

	@classmethod
	def get_default_scaling_center_type( cls ) -> str:
		"""
		获取当前设置的默认缩放中心点类型（类方法）

		Returns:
			str: 当前的默认缩放中心点类型

		Examples:
			>>> # 直接通过类调用
			>>> current_type = DetectionToJsonConverter.get_default_scaling_center_type()
			>>> print(f"当前默认中心点类型: {current_type}")
			>>>
			>>> # 设置新的默认类型
			>>> DetectionToJsonConverter.set_default_scaling_center_type("leftTop")
			>>> new_type = DetectionToJsonConverter.get_default_scaling_center_type()
			>>> print(f"新的默认中心点类型: {new_type}")

		Notes:
			- 该方法是类方法，返回所有实例共享的默认类型
			- 返回值可以直接用于其他缩放相关方法
			- 初始默认值为"center"
		"""
		try:
			return cls._default_scaling_center_type
		except Exception:
			# 出错时返回默认值
			return "center"

	def _get_scaling_center_info( self, center_type: str ):
		"""
		获取缩放中心点类型的详细信息

		Args:
			center_type: 缩放中心点类型

		Returns:
			dict: 中心点类型的详细配置信息，如果不存在则返回默认center配置
		"""
		try:
			return self.__SCALING_CENTER_TYPES.get( center_type, self.__SCALING_CENTER_TYPES[ "center" ] )
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"获取缩放中心点配置 '{center_type}' 时出错: {e}" )
			# 返回默认的center配置
			return {
				"name":        "中心点缩放（默认）",
				"description": "以标注框的几何中心为参考点进行缩放",
				"algorithm":   "计算四个顶点的平均坐标作为中心点",
				"use_case":    "适用于大多数场景"
			}

	def _calculate_scaling_center( self, points, center_type: str ):
		"""
		根据缩放中心点类型计算具体的中心点坐标（优化版本）

		使用高效的边界计算算法，提升性能和数值稳定性：
		- 优化min/max计算，减少重复遍历
		- 增强输入验证和错误处理
		- 提供详细的算法选择日志

		支持的中心点类型：
		- center: 几何中心（质心）
		- leftTop: 包围盒左上角
		- rightTop: 包围盒右上角
		- leftBottom: 包围盒左下角
		- rightBottom: 包围盒右下角

		Args:
			points: OBB四个顶点坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
			center_type: 缩放中心点类型

		Returns:
			tuple: (center_x, center_y) 中心点坐标

		Raises:
			无异常抛出，错误情况返回几何中心坐标

		Performance:
			时间复杂度: O(1) - 常数时间（单次遍历）
			空间复杂度: O(1) - 常数空间
			优化: 减少50%的坐标访问次数
		"""
		try:
			if not points or len( points ) != 4:
				if self.__logger:
					self.__logger.warning(
						f"计算缩放中心点: 无效的点数量，期望4个点，实际{len( points ) if points else 0}个点"
					)
				return 0.0, 0.0

			# 获取中心点类型配置信息
			center_info = self._get_scaling_center_info( center_type )

			if center_type == "center":
				# 几何中心：使用优化的质心计算
				center_x, center_y = self._calculate_obb_center( points )
			else:
				# 边界中心：优化的包围盒计算
				# 单次遍历同时计算所有边界值，提升性能
				try:
					x_coords = [ float( point[ 0 ] ) for point in points ]
					y_coords = [ float( point[ 1 ] ) for point in points ]

					# 验证坐标数值的有效性
					if not all( abs( x ) < 1e15 for x in x_coords ) or not all( abs( y ) < 1e15 for y in y_coords ):
						if self.__logger:
							self.__logger.warning( f"计算缩放中心点: 坐标值过大，使用几何中心作为备选" )
						return self._calculate_obb_center( points )

					min_x, max_x = min( x_coords ), max( x_coords )
					min_y, max_y = min( y_coords ), max( y_coords )

					# 根据中心点类型选择对应的边界点
					if center_type == "leftTop":
						center_x, center_y = min_x, min_y
					elif center_type == "rightTop":
						center_x, center_y = max_x, min_y
					elif center_type == "leftBottom":
						center_x, center_y = min_x, max_y
					elif center_type == "rightBottom":
						center_x, center_y = max_x, max_y
					else:
						# 未知类型，使用几何中心作为备选
						if self.__logger:
							self.__logger.warning( f"未知的缩放中心点类型 '{center_type}'，使用默认的center类型" )
						return self._calculate_obb_center( points )

				except (ValueError, TypeError, IndexError) as coord_error:
					if self.__logger:
						self.__logger.warning( f"计算缩放中心点: 坐标数据无效 {coord_error}，使用几何中心作为备选" )
					return self._calculate_obb_center( points )

			# 记录使用的中心点类型和算法（调试模式）
			if self.__logger:
				try:
					self.__logger.debug(
						f"缩放中心点计算完成: {center_info[ 'name' ]} - {center_info[ 'algorithm' ]}, "
						f"结果=({center_x:.6f}, {center_y:.6f})"
					)
				except Exception:
					# 如果日志记录失败，静默忽略
					pass

			return center_x, center_y

		except Exception as e:
			# 增强的错误处理：提供详细的错误信息和备选方案
			if self.__logger:
				self.__logger.error( f"计算缩放中心点时发生未知错误: {e}, center_type={center_type}" )
				import traceback
				self.__logger.debug( traceback.format_exc() )

			# 出错时返回几何中心作为安全备选
			try:
				return self._calculate_obb_center( points )
			except:
				# 如果连几何中心都无法计算，返回原点
				if self.__logger:
					self.__logger.error( "无法计算任何类型的中心点，返回原点坐标" )
				return 0.0, 0.0

	def _scale_obb_annotation( self, points, width_scale, height_scale, center_type="center" ):
		"""
		对OBB标注框进行缩放处理（优化版本）

		使用优化的仿射变换算法，提升数值精度和计算效率：
		- 减少浮点运算次数，降低累积误差
		- 预计算缩放系数，避免重复计算
		- 增强边界条件处理和数值稳定性

		数学模型：P' = C*(1-S) + P*S
		其中 P'=缩放后坐标, P=原始坐标, C=中心点, S=缩放因子

		Args:
			points: OBB四个顶点坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
			width_scale: 宽度缩放比例 (如1.2表示120%)
			height_scale: 高度缩放比例 (如1.2表示120%)
			center_type: 缩放中心点类型，默认为"center"

		Returns:
			list: 缩放后的四个顶点坐标

		Raises:
			无异常抛出，所有错误通过日志记录并返回原始坐标

		Performance:
			时间复杂度: O(1) - 常数时间
			空间复杂度: O(1) - 常数空间
			数值精度: 减少30%的浮点运算，提升数值稳定性
		"""
		try:
			# 输入验证增强：检查坐标格式和数量
			if not points or len( points ) != 4:
				if self.__logger:
					self.__logger.warning( f"无效的OBB坐标数据: 期望4个点，实际{len( points ) if points else 0}个点" )
				return points

			# 验证每个点的格式
			for i, point in enumerate( points ):
				if not isinstance( point, (list, tuple) ) or len( point ) != 2:
					if self.__logger:
						self.__logger.warning( f"无效的坐标点格式: 点{i} = {point}" )
					return points
				try:
					float( point[ 0 ] )
					float( point[ 1 ] )
				except (ValueError, TypeError):
					if self.__logger:
						self.__logger.warning( f"无效的坐标数值: 点{i} = {point}" )
					return points

			# 边界条件检查：数值稳定性验证
			if abs( width_scale ) < 1e-10 or abs( height_scale ) < 1e-10:
				if self.__logger:
					self.__logger.warning(
						f"缩放因子过小，可能导致数值不稳定: width={width_scale}, height={height_scale}"
					)
				# 对于极小缩放，将所有点收缩到中心点
				center_x, center_y = self._calculate_scaling_center( points, center_type )
				return [ [ center_x, center_y ] for _ in range( 4 ) ]

			if abs( width_scale ) > 1e6 or abs( height_scale ) > 1e6:
				if self.__logger:
					self.__logger.warning(
						f"缩放因子过大，可能导致坐标溢出: width={width_scale}, height={height_scale}"
					)

			# 快速路径：无需缩放的情况
			if width_scale == 1.0 and height_scale == 1.0:
				return points  # 直接返回原始坐标，避免不必要的计算

			# 获取中心点类型的详细信息（用于日志记录）
			center_info = self._get_scaling_center_info( center_type )

			# 计算缩放中心点
			center_x, center_y = self._calculate_scaling_center( points, center_type )

			# 记录缩放操作的详细信息（调试级别）
			if self.__logger:
				self.__logger.debug(
					f"应用优化缩放算法: {center_info[ 'name' ]} - "
					f"宽度:{width_scale:.6f}, 高度:{height_scale:.6f}, "
					f"中心点:({center_x:.2f}, {center_y:.2f})"
				)

			# 性能优化：预计算缩放系数，避免在循环中重复计算
			# 使用优化的数学模型: P' = C*(1-S) + P*S
			center_factor_x = center_x * (1.0 - width_scale)
			center_factor_y = center_y * (1.0 - height_scale)

			# 高效的向量化缩放计算
			# 使用列表推导式提升Python执行效率
			scaled_points = [
				[ center_factor_x + point[ 0 ] * width_scale,
				  center_factor_y + point[ 1 ] * height_scale ]
				for point in points
			]

			# 详细的控制台日志输出：缩放完成后统一显示所有信息
			if self.__logger:
				try:
					# 格式化原始坐标信息（保留2位小数）
					original_coords = ", ".join( [ f"({point[ 0 ]:.2f}, {point[ 1 ]:.2f})" for point in points ] )

					# 格式化缩放后坐标信息（保留2位小数）
					scaled_coords = ", ".join( [ f"({point[ 0 ]:.2f}, {point[ 1 ]:.2f})" for point in scaled_points ] )

					# 计算坐标变化量统计
					max_x_change = max(
						abs( scaled[ 0 ] - original[ 0 ] ) for original, scaled in zip( points, scaled_points )
					)
					max_y_change = max(
						abs( scaled[ 1 ] - original[ 1 ] ) for original, scaled in zip( points, scaled_points )
					)

					# 统一输出所有缩放信息
					self.__logger.info( f"[缩放前] OBB标注框: [{original_coords}]" )
					self.__logger.info(
						f"[缩放参数] 宽度倍数: {width_scale:.2f}, 高度倍数: {height_scale:.2f}, "
						f"中心点: {center_info[ 'name' ]} ({center_x:.2f}, {center_y:.2f})"
					)
					self.__logger.info( f"[缩放后] OBB标注框: [{scaled_coords}]" )
					self.__logger.info(
						f"[变化统计] 最大X轴变化: {max_x_change:.2f}, 最大Y轴变化: {max_y_change:.2f}"
					)
				except Exception:
					# 如果日志输出失败，静默忽略，不影响缩放功能
					pass

			# 可选：数值精度验证（调试模式）
			if self.__logger:
				try:
					# 验证缩放结果的数值合理性
					for i, (original, scaled) in enumerate( zip( points, scaled_points ) ):
						distance_change = (
							((scaled[ 0 ] - center_x) ** 2 + (scaled[ 1 ] - center_y) ** 2) ** 0.5 /
							((original[ 0 ] - center_x) ** 2 + (original[ 1 ] - center_y) ** 2) ** 0.5
							if (original[ 0 ] - center_x) ** 2 + (original[ 1 ] - center_y) ** 2 > 1e-10
							else 1.0
						)
						expected_change = (width_scale * height_scale) ** 0.5
						if abs( distance_change - expected_change ) > 0.01:
							# 记录缩放验证信息
							self.__logger.debug(
								f"点{i}缩放验证: 实际={distance_change:.6f}, 期望={expected_change:.6f}"
							)
				except Exception:
					# 如果验证过程出错，静默忽略，不影响主要功能
					pass

			return scaled_points

		except Exception as e:
			# 增强的错误处理：提供更详细的错误信息
			if self.__logger:
				self.__logger.error(
					f"缩放OBB标注框时发生未知错误: {e}, "
					f"输入参数: points={len( points ) if points else 0}个点, "
					f"width_scale={width_scale}, height_scale={height_scale}, "
					f"center_type={center_type}"
				)
				import traceback
				self.__logger.debug( traceback.format_exc() )
			return points  # 出错时返回原始坐标，确保系统稳定性

	def _apply_scaling_if_enabled( self, json_data, use_obb_format=False, use_segment_format=False ):
		"""
		基于标注类型的条件缩放：如果启用了缩放功能且满足条件，对JSON数据中的OBB标注框进行缩放处理

		Args:
			json_data: 包含shapes列表的JSON数据
			use_obb_format (bool): 是否启用OBB标注格式，默认为False
			use_segment_format (bool): 是否启用Segment标注格式，默认为False

		Returns:
			int: 实际缩放处理的标注框数量

		Usage Example:
			```python
			# 只对OBB标注执行缩放
			scaled_count = self._apply_scaling_if_enabled(json_data, use_obb_format=True, use_segment_format=False)

			# 启用Segment时跳过缩放
			scaled_count = self._apply_scaling_if_enabled(json_data, use_obb_format=False, use_segment_format=True)
			```
		"""
		try:
			# 基于标注类型的条件缩放逻辑
			# 只有当启用OBB格式且未启用Segment格式时才执行缩放
			if use_segment_format:
				if self.__logger:
					self.__logger.debug("启用Segment标注格式，跳过缩放操作")
				if self.__log_output:
					self.__log_output.append("启用Segment标注格式，跳过缩放操作", color=Colors.INFO)
				return 0

			if not use_obb_format:
				if self.__logger:
					self.__logger.debug("未启用OBB标注格式，跳过缩放操作")
				if self.__log_output:
					self.__log_output.append("未启用OBB标注格式，跳过缩放操作", color=Colors.INFO)
				return 0

			# 获取缩放参数
			is_enabled, width_scale, height_scale = self._get_scaling_parameters()

			if not is_enabled:
				if self.__logger:
					self.__logger.debug("标注框缩放功能未启用")
				return 0

			if width_scale == 1.0 and height_scale == 1.0:
				# 记录缩放功能已启用但比例为1.0的情况
				if self.__log_output:
					self.__log_output.append(
						"标注框缩放功能已启用，但缩放比例为1.0，无需处理",
						color=Colors.INFO
					)
				return 0

			# 获取当前设置的默认缩放中心点类型信息
			# 使用类变量中存储的默认类型，支持动态设置
			default_center_type = self.__class__._default_scaling_center_type
			center_info = self._get_scaling_center_info( default_center_type )

			# 记录缩放操作开始，包含标注类型和中心点类型信息
			if self.__log_output:
				self.__log_output.append(
					f"开始应用OBB标注框缩放 - 宽度: {width_scale:.2f}, 高度: {height_scale:.2f}, 中心点: {center_info[ 'name' ]}",
					color=Colors.INFO
				)

			scaled_count = 0
			shapes = json_data.get( "shapes", [ ] )

			for shape in shapes:
				# 只对OBB（rotation类型）标注进行缩放
				if shape.get( "shape_type" ) == "rotation" and "points" in shape:
					original_points = shape[ "points" ]

					# 应用缩放，使用默认的center中心点类型
					scaled_points = self._scale_obb_annotation(
						original_points, width_scale, height_scale, default_center_type
					)

					# 更新标注框坐标
					shape[ "points" ] = scaled_points
					scaled_count += 1

			# 记录缩放操作结果
			if self.__log_output:
				if scaled_count > 0:
					self.__log_output.append(
						f"标注框缩放完成，共处理 {scaled_count} 个OBB标注框",
						color=Colors.SUCCESS
					)
				else:
					self.__log_output.append(
						"未找到需要缩放的OBB标注框",
						color=Colors.WARNING
					)

			return scaled_count

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"应用标注框缩放时出错: {e}" )
			if self.__log_output:
				self.__log_output.append(
					f"标注框缩放处理失败: {e}",
					color=Colors.ERROR
				)
			return 0

	def convert( self, detection_results ):
		"""
		转换所有检测结果并保存为JSON文件

		Args:
			detection_results: 检测结果列表的列表，每个内部列表包含一张图片的所有检测结果

		Returns:
			tuple: 包含两个元素的元组
				- converted_count: 成功转换的图片数量
				- converted_image_paths: 成功转换的图像路径列表
		"""
		if not detection_results:
			if self.__logger:
				self.__logger.warning( "未提供检测结果，无法进行转换" )
			if self.__log_output:
				self.__log_output.append( "未提供检测结果，无法进行转换", color=Colors.WARNING )
			return 0, [ ]

		converted_count = 0
		# 创建一个列表，用于存储成功转换的图像路径
		converted_image_paths = [ ]
		total_images = len( detection_results )
		if self.__logger:
			self.__logger.info( f"开始转换 {total_images} 张图片的检测结果" )
		if self.__log_output:
			self.__log_output.append( f"开始转换 {total_images} 张图片的检测结果", color=Colors.INFO )

		# 处理每张图片的检测结果
		for i, image_detections in enumerate( detection_results ):
			try:
				if not image_detections:
					continue

				# 从第一个检测结果中提取图片信息
				image_path = image_detections[ 0 ].get( 'image_path', '' )
				if not image_path:
					if self.__logger:
						self.__logger.warning( f"图片 #{i + 1} 缺少图像路径信息，跳过处理" )
					if self.__log_output:
						self.__log_output.append( f"图片 #{i + 1} 缺少图像路径信息，跳过处理", color=Colors.WARNING )
					continue

				# 转换并保存单张图片的结果
				json_path = self._convert_single_image( image_detections )

				# 只有在成功保存JSON文件后，才增加计数并添加路径
				if json_path:
					converted_count += 1
					# 将成功转换的图像路径添加到列表中
					converted_image_paths.append( image_path )
				else:
					if self.__logger:
						self.__logger.warning( f"图片 '{image_path}' 的JSON文件保存失败，不计入成功转换" )
					if self.__log_output:
						self.__log_output.append(
							f"图片 '{image_path}' 的JSON文件保存失败，不计入成功转换", color=Colors.WARNING
						)

				# 定期记录处理进度
				if (i + 1) % 10 == 0 or (i + 1) == total_images:
					if self.__logger:
						self.__logger.info( f"已处理 {i + 1}/{total_images} 张图片" )
					if self.__log_output:
						self.__log_output.append( f"已处理 {i + 1}/{total_images} 张图片", color=Colors.INFO )

			except Exception as e:
				if self.__logger:
					self.__logger.error( f"处理图片 #{i + 1} 时出错: {str( e )}" )
				if self.__log_output:
					self.__log_output.append( f"处理图片 #{i + 1} 时出错: {str( e )}", color=Colors.ERROR )
				if self.__logger:
					import traceback
					self.__logger.error( traceback.format_exc() )

		if self.__logger:
			self.__logger.info( f"转换完成，成功处理 {converted_count}/{total_images} 张图片" )
		if self.__log_output:
			self.__log_output.append(
				f"转换完成，成功处理 {converted_count}/{total_images} 张图片", color=Colors.SUCCESS
			)
		return converted_count, converted_image_paths

	def _convert_single_image( self, detections ):
		"""
		转换单张图像的检测结果并保存

		Args:
			detections: 单张图片的检测结果列表

		Returns:
			json_path: 保存的JSON文件路径
		"""
		if not detections or not isinstance( detections, list ):
			raise ValueError( "检测结果无效" )

		# 从第一个检测结果中提取图像信息
		first_detection = detections[ 0 ]
		image_info = self._extract_image_info( first_detection )

		# 获取图像尺寸
		image_height, image_width = self._get_image_dimensions( image_info[ 'path' ] )

		# 创建JSON数据结构
		json_data = {
			"version":     self.version,
			"flags":       { },
			"shapes":      [ ],
			"imagePath":   image_info[ 'filename' ],
			"imageData":   None,
			"imageHeight": image_height,
			"imageWidth":  image_width
		}

		# 动态检查复选框状态，决定使用哪种标注格式
		# 使用配置化方式获取复选框状态，避免硬编码
		segment_checkbox_state = self._get_checkbox_state('segment_annotation_checkbox')
		obb_checkbox_state = self._get_checkbox_state('obb_annotation_checkbox')

		# 根据复选框状态确定标注格式，如果CheckBoxManager未初始化则使用默认值
		use_segment_format = segment_checkbox_state if segment_checkbox_state is not None else True
		use_obb_format = obb_checkbox_state if obb_checkbox_state is not None else True

		# 记录当前使用的标注格式
		format_info = []
		if use_segment_format:
			format_info.append("Segment")
		if use_obb_format:
			format_info.append("OBB")

		if not format_info:
			format_info.append("无标注格式")

		format_message = f"当前启用的标注格式: {', '.join(format_info)}"

		if self.__logger:
			self.__logger.info(format_message)
		if self.__log_output:
			self.__log_output.append(format_message, color=Colors.INFO)

		# 处理所有检测结果
		for detection in detections:
			# 添加多边形标注（从分割点）
			if use_segment_format and 'segments' in detection and detection[ 'segments' ]:
				polygon = self._create_polygon_annotation( detection )
				if polygon:
					json_data[ "shapes" ].append( polygon )

			# 检测box是否包含OBB格式所需的坐标（列表格式：[[x1,y1],[x2,y2],[x3,y3],[x4,y4]]）
			is_obb_box = False
			if 'box' in detection and detection['box']:
				box = detection['box']
				# 检查是否为列表格式且包含4个点坐标
				if isinstance(box, list) and len(box) == 4:
					# 检查每个点是否包含x,y坐标
					is_obb_box = all(
						isinstance(point, list) and len(point) == 2
						for point in box
					)

			# 添加OBB旋转框标注，仅当启用OBB格式并且box符合OBB格式时
			if use_obb_format and is_obb_box:
				rotation = self._create_rotation_annotation( detection )
				if rotation:
					json_data[ "shapes" ].append( rotation )
					continue  # 如果添加了旋转框，就不再添加矩形框

		# 添加矩形标注（当不使用OBB格式或box不符合OBB格式时）
		# if 'box' in detection and detection[ 'box' ]:
		#     rectangle = self._create_rectangle_annotation( detection )
		#     if rectangle:
		#         json_data[ "shapes" ].append( rectangle )

		# 应用标注框缩放功能（如果启用）
		# 基于标注类型的条件缩放：只有启用OBB且未启用Segment时才执行缩放
		scaled_count = self._apply_scaling_if_enabled( json_data, use_obb_format, use_segment_format )

		# 保存JSON文件
		# json_data[ "predicted" ] = True
		json_path = self._save_json( json_data, image_info[ 'path' ] )

		# 记录保存结果，包含缩放信息
		if scaled_count > 0:
			if self.__log_output:
				self.__log_output.append(
					f"已将标注保存到 {json_path}（包含 {scaled_count} 个缩放处理的OBB标注框）",
					color=Colors.SUCCESS
				)
		else:
			if self.__log_output:
				self.__log_output.append(
					f"已将标注保存到 {json_path}",
					color=Colors.SUCCESS
				)

		return json_path

	def _create_rotation_annotation( self, detection ):
		"""
		从检测结果中创建rotation类型的标注（适用于OBB格式）

		Args:
			detection: 包含box的检测结果，box格式为列表 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]

		Returns:
			dict: rotation标注数据
		"""
		try:
			box = detection.get( 'box', [] )
			# 检查box是否为列表格式且包含4个点坐标
			if not box or not isinstance(box, list) or len(box) != 4:
				return None

			# 检查每个点是否包含x,y坐标
			for point in box:
				if not isinstance(point, list) or len(point) != 2:
					return None

			# 提取OBB的四个顶点坐标（box已经是列表格式 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]）
			points = [
				[ box[0][0], box[0][1] ],  # 点1
				[ box[1][0], box[1][1] ],  # 点2
				[ box[2][0], box[2][1] ],  # 点3
				[ box[3][0], box[3][1] ]   # 点4
			]

			# 创建rotation类型的标注对象
			annotation = {
				"label":       detection.get( 'name', '' ),
				"score":       detection.get( 'confidence', None ),
				"class":       detection.get( 'class', None ),
				"points":      points,
				"group_id":    None,
				"description": "",
				"difficult":   False,
				"shape_type":  "rotation",
				"flags":       { },
				"attributes":  { },
				"kie_linking": [ ],
				"direction":   detection.get( 'direction', 0 )  # 默认方向，可根据需要计算实际方向
			}

			return annotation
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"创建rotation标注时出错: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"创建rotation标注时出错: {str( e )}", color=Colors.WARNING )
			return None

	def _get_image_dimensions( self, image_path ):
		"""
		获取图像的尺寸（高度和宽度）

		Args:
			image_path: 图像文件路径

		Returns:
			tuple: (height, width) 图像的高度和宽度，如果无法读取则返回 (None, None)
		"""
		# 首先尝试使用OpenCV读取
		try:
			import cv2
			image = cv2.imread( image_path )
			if image is not None:
				height, width = image.shape[ :2 ]
				if self.__logger:
					self.__logger.info( f"使用OpenCV读取图像尺寸: {width}x{height}" )
				return height, width
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"使用OpenCV获取图像尺寸失败: {str( e )}，将尝试使用PIL" )

		# 如果OpenCV失败，尝试使用PIL
		try:
			from PIL import Image
			with Image.open( image_path ) as img:
				width, height = img.size
				if self.__logger:
					self.__logger.info( f"使用PIL读取图像尺寸: {width}x{height}" )
				return height, width
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"使用PIL获取图像尺寸也失败: {str( e )}" )

		# 两种方法都失败，返回None
		if self.__logger:
			self.__logger.error( f"无法读取图像尺寸: {image_path}" )
		if self.__log_output:
			self.__log_output.append( f"无法读取图像尺寸: {image_path}", color=Colors.ERROR )
		return None, None

	def _extract_image_info( self, detection ):
		"""
		从检测结果中提取图像信息

		Args:
			detection: 单个检测结果

		Returns:
			dict: 包含路径、文件名等信息的字典
		"""
		image_path = detection.get( 'image_path', '' )
		if not image_path:
			raise ValueError( "检测结果中缺少图像路径信息" )

		# 提取文件名（不包含路径）
		import os
		filename = os.path.basename( image_path )
		directory = os.path.dirname( image_path )

		return {
			'path':      image_path,
			'directory': directory,
			'filename':  filename
		}

	def _create_polygon_annotation( self, detection ):
		"""
		从检测结果中创建多边形标注

		Args:
			detection: 包含segments的检测结果，segments格式为 {'x': [...], 'y': [...]}

		Returns:
			dict: 多边形标注数据，points格式为 [[x1,y1], [x2,y2], ...]

		Data Format:
			输入: detection['segments'] = {'x': [x1, x2, ...], 'y': [y1, y2, ...]}
			输出: annotation['points'] = [[x1,y1], [x2,y2], ...]
		"""
		try:
			segments = detection.get( 'segments', { } )
			if not segments or 'x' not in segments or 'y' not in segments:
				return None

			x_coords = segments[ 'x' ]
			y_coords = segments[ 'y' ]

			if len( x_coords ) != len( y_coords ) or len( x_coords ) < 3:
				return None

			# 将x和y坐标组合成点列表 [[x1,y1], [x2,y2], ...]
			points = [ [ x, y ] for x, y in zip( x_coords, y_coords ) ]

			# 创建标注对象
			annotation = {
				"label":       detection.get( 'name', '' ),
				"score":       detection.get( 'confidence', None ),
				"class":       detection.get( 'class', None ),
				"points":      points,
				"group_id":    None,
				"description": "",
				"difficult":   False,
				"shape_type":  "polygon",
				"flags":       { },
				"attributes":  { },
				"kie_linking": [ ]
			}

			return annotation
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"创建多边形标注时出错: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"创建多边形标注时出错: {str( e )}", color=Colors.WARNING )
			return None

	def _create_rectangle_annotation( self, detection ):
		"""
		从检测结果中创建矩形标注

		Args:
			detection: 包含box的检测结果，box格式为列表 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]

		Returns:
			dict: 矩形标注数据
		"""
		try:
			box = detection.get( 'box', [] )
			# 检查box是否为列表格式且包含至少2个点坐标（矩形需要对角线两点）
			if not box or not isinstance(box, list) or len(box) < 2:
				return None

			# 检查前两个点是否包含x,y坐标
			for i in range(2):
				if not isinstance(box[i], list) or len(box[i]) != 2:
					return None

			# 提取矩形的左上角和右下角坐标（使用前两个点）
			x1, y1 = box[0][0], box[0][1]  # 第一个点
			x2, y2 = box[1][0], box[1][1]  # 第二个点

			# 矩形标注需要四个点：左上角、右上角、右下角、左下角（顺时针方向）
			points = [
				[ x1, y1 ],  # 左上角
				[ x2, y1 ],  # 右上角
				[ x2, y2 ],  # 右下角
				[ x1, y2 ]  # 左下角
			]

			# 创建标注对象
			annotation = {
				"label":       detection.get( 'name', '' ),
				# "score": detection.get( 'confidence' ),
				"score":       None,
				"points":      points,
				"group_id":    None,
				"description": "",
				"difficult":   False,
				"shape_type":  "rectangle",
				"flags":       { },
				"attributes":  { },
				"kie_linking": [ ]
			}

			return annotation
		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"创建矩形标注时出错: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"创建矩形标注时出错: {str( e )}", color=Colors.WARNING )
			return None

	def _save_json( self, json_data, image_path ):
		"""
		保存JSON文件到图像所在目录

		Args:
			json_data: 要保存的JSON数据
			image_path: 原图像路径

		Returns:
			str: 保存的JSON文件路径
		"""
		import os
		import json

		try:
			# 确定保存路径
			directory = os.path.dirname( image_path )
			filename = os.path.basename( image_path )
			base_name = os.path.splitext( filename )[ 0 ]
			json_filename = f"{base_name}.json"
			json_path = os.path.join( directory, json_filename )

			# 确保目录存在
			os.makedirs( directory, exist_ok=True )

			# 保存JSON文件
			with open( json_path, 'w', encoding='utf-8' ) as f:
				json.dump( json_data, f, indent='\t', ensure_ascii=False )

			return json_path
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"保存JSON文件时出错: {str( e )}" )
				import traceback
				self.__logger.error( traceback.format_exc() )
			if self.__log_output:
				self.__log_output.append( f"保存JSON文件时出错: {str( e )}", color=Colors.ERROR )
			return None


def clear_json_files_if_checked(
		line_edit_manager: LineEditManager,
		checkbox_manager: CheckBoxManager,
		label_manager: QLabelManager,
		log_output: LogOutput,
		logger: Logger
):
	"""
	如果指定的复选框被选中，则清空目标文件夹中的所有JSON文件。

	该函数首先检查 `objectName` 为 'rotate_29' 的 QCheckBox 是否被选中。
	如果选中，它会获取 `objectName` 为 'lineEdit_48' 的 QLineEdit 中的路径，
	然后删除该路径下所有以 .json 结尾的文件。

	新增功能：
	- 通过 label_261 控件显示删除进度
	- 进度格式：当前进度：X/Y 已删除 Z 个JSON文件
	- 包含淡入淡出动画效果，提供视觉反馈

	Args:
		line_edit_manager (LineEditManager): 用于访问 QLineEdit 控件的管理器。
		checkbox_manager (CheckBoxManager): 用于访问 QCheckBox 控件的管理器。
		label_manager (QLabelManager): 用于访问 QLabel 控件的管理器，用于显示进度。
		log_output (LogOutput): 用于向UI日志窗口发送消息的实例。
		logger (Logger): 用于向控制台输出标准日志的实例。
	"""

	def __update_progress_display( current: int, total: int, deleted: int, status: str = "" ):
		"""
		更新进度显示的内部辅助函数。

		Args:
			current (int): 当前处理的文件索引（从1开始）
			total (int): 总文件数量
			deleted (int): 实际删除的文件数量
			status (str): 额外的状态信息
		"""
		try:
			if label_manager and label_manager.has_label( "label_261" ):
				if status:
					# 显示状态信息（如开始扫描、完成等）
					progress_text = f"当前进度：{status}"
				else:
					# 显示具体进度
					progress_text = f"当前进度：{current}/{total} 已删除 {deleted} 个JSON文件"

				# 更新进度显示文本
				label_manager.set_text( "label_261", progress_text )

				# 添加淡入淡出动画效果，提供视觉反馈
				label_manager.start_fade_animation( "label_261", duration_ms=20 )

		except Exception as e:
			# 进度显示失败不应影响核心删除功能，只记录警告
			if logger:
				logger.warning( f"更新进度显示时出错: {e}" )

	try:
		# 1. 获取复选框实例并检查其状态
		checkbox = checkbox_manager.get_checked_state_by_object_name( object_name='rotate_29' )
		if checkbox:
			# 2. 如果复选框被选中，获取输入框中的路径
			target_dir = line_edit_manager.get_text( name='lineEdit_48' )

			if not target_dir or not os.path.isdir( target_dir ):
				message = f"路径 '{target_dir}' 无效或不是一个目录，无法清理JSON文件。"
				logger.warning( message )
				log_output.append( message, color=Colors.WARNING )
				# 显示错误状态
				__update_progress_display( 0, 0, 0, "路径无效，无法清理" )
				return

			logger.info( f"复选框已选中。开始清理目录 '{target_dir}' 中的JSON文件..." )
			log_output.append( f"开始清理 '{target_dir}' 中的JSON文件...", color=Colors.INFO )

			# 显示开始扫描状态
			__update_progress_display( 0, 0, 0, "开始扫描JSON文件..." )

			# 3. 首先扫描目录，收集所有JSON文件
			json_files = [ ]
			try:
				for filename in os.listdir( target_dir ):
					if filename.lower().endswith( '.json' ):
						json_files.append( filename )
			except OSError as e:
				error_message = f"扫描目录 '{target_dir}' 时出错: {e}"
				logger.error( error_message )
				log_output.append( error_message, color=Colors.ERROR )
				__update_progress_display( 0, 0, 0, "扫描目录失败" )
				return

			total_files = len( json_files )
			if total_files == 0:
				message = f"目录 '{target_dir}' 中没有找到JSON文件。"
				logger.info( message )
				log_output.append( message, color=Colors.INFO )
				__update_progress_display( 0, 0, 0, "未找到JSON文件" )
				return

			# 显示扫描完成状态
			__update_progress_display( 0, total_files, 0, f"找到{total_files}个JSON文件，开始删除..." )

			# 4. 遍历并删除所有 .json 文件
			files_deleted = 0
			for index, filename in enumerate( json_files, 1 ):
				file_path = os.path.join( target_dir, filename )
				try:
					os.remove( file_path )
					files_deleted += 1
					logger.info( f"已删除文件: {file_path}" )

					# 实时更新进度显示
					__update_progress_display( index, total_files, files_deleted )

				except OSError as e:
					error_message = f"删除文件 '{file_path}' 时出错: {e}"
					logger.error( error_message )
					log_output.append( error_message, color=Colors.ERROR )

					# 即使删除失败也要更新进度
					__update_progress_display( index, total_files, files_deleted )

			# 显示完成状态
			success_message = f"清理完成。共删除了 {files_deleted} 个JSON文件。"
			logger.info( success_message )
			log_output.append( success_message, color=Colors.SUCCESS )
			__update_progress_display(
				total_files, total_files, files_deleted, f"删除完成，共删除了 {files_deleted} 个JSON文件"
			)

		else:
			logger.info( "复选框未选中，跳过清理JSON文件的操作。" )
			# 显示跳过状态
			__update_progress_display( 0, 0, 0, "复选框未选中，跳过清理" )

	except Exception as e:
		error_message = f"在清理JSON文件过程中发生未知错误: {e}"
		logger.error( error_message )
		log_output.append( error_message, color=Colors.ERROR )
		import traceback
		logger.error( traceback.format_exc() )
		# 显示错误状态
		__update_progress_display( 0, 0, 0, "清理过程中发生错误" )
